"""
OCR引擎 - 统一的OCR服务入口

提供高级的OCR服务接口，管理多个OCR模型。
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Union
import numpy as np

from .interfaces import OCRInterface, TextRegion, OCRResult
from .base_ocr import BaseOCR
from ..config import OCRConfig, OCRModelType
from ..models.model_registry import ModelRegistry


class OCREngine:
    """
    OCR引擎
    
    统一的OCR服务入口，支持：
    - 多模型管理
    - 自动模型选择
    - 批量处理
    - 性能监控
    """
    
    def __init__(self, config: Optional[OCRConfig] = None):
        """
        初始化OCR引擎
        
        Args:
            config: OCR配置，如果为None则使用默认配置
        """
        self.config = config or OCRConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 模型管理
        self._models: Dict[str, OCRInterface] = {}
        self._current_model: Optional[OCRInterface] = None
        self._model_registry = ModelRegistry()
        
        # 性能统计
        self._stats = {
            'total_requests': 0,
            'total_processing_time': 0.0,
            'average_processing_time': 0.0,
            'error_count': 0
        }
    
    async def initialize(self, device: str = 'cpu') -> None:
        """
        初始化OCR引擎
        
        Args:
            device: 设备类型
        """
        self.logger.info("初始化OCR引擎")
        
        try:
            # 创建并加载默认模型
            model_class = self._model_registry.get_model_class(self.config.model_type.value)
            if model_class is None:
                raise ValueError(f"不支持的模型类型: {self.config.model_type}")
            
            model = model_class(self.config)
            await model.load_model(device)
            
            self._models[self.config.model_type.value] = model
            self._current_model = model
            
            self.logger.info(f"OCR引擎初始化完成，使用模型: {self.config.model_type.value}")
            
        except Exception as e:
            self.logger.error(f"OCR引擎初始化失败: {e}")
            raise
    
    async def recognize(
        self, 
        image: np.ndarray, 
        text_regions: List[TextRegion],
        model_type: Optional[OCRModelType] = None,
        **kwargs
    ) -> OCRResult:
        """
        执行OCR识别
        
        Args:
            image: 输入图像
            text_regions: 文本区域列表
            model_type: 指定使用的模型类型，如果为None则使用默认模型
            **kwargs: 额外参数
            
        Returns:
            OCRResult: 识别结果
        """
        if not self._current_model:
            raise RuntimeError("OCR引擎未初始化，请先调用 initialize()")
        
        # 选择模型
        model = self._get_model(model_type)
        
        try:
            # 更新统计
            self._stats['total_requests'] += 1
            
            # 执行识别
            result = await model.recognize(image, text_regions, **kwargs)
            
            # 更新性能统计
            self._update_stats(result.processing_time)
            
            return result
            
        except Exception as e:
            self._stats['error_count'] += 1
            self.logger.error(f"OCR识别失败: {e}")
            raise
    
    async def recognize_batch(
        self, 
        images: List[np.ndarray], 
        text_regions_list: List[List[TextRegion]],
        model_type: Optional[OCRModelType] = None,
        max_concurrent: int = 4,
        **kwargs
    ) -> List[OCRResult]:
        """
        批量OCR识别
        
        Args:
            images: 图像列表
            text_regions_list: 文本区域列表的列表
            model_type: 指定使用的模型类型
            max_concurrent: 最大并发数
            **kwargs: 额外参数
            
        Returns:
            List[OCRResult]: 识别结果列表
        """
        if len(images) != len(text_regions_list):
            raise ValueError("图像数量和文本区域列表数量不匹配")
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single(image, regions):
            async with semaphore:
                return await self.recognize(image, regions, model_type, **kwargs)
        
        # 并发处理
        tasks = [
            process_single(img, regions) 
            for img, regions in zip(images, text_regions_list)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"批量处理第{i}项失败: {result}")
                # 创建空结果
                final_results.append(OCRResult(
                    regions=[],
                    processing_time=0.0,
                    model_info={'error': str(result)}
                ))
            else:
                final_results.append(result)
        
        return final_results
    
    async def switch_model(self, model_type: OCRModelType, device: str = 'cpu') -> None:
        """
        切换OCR模型
        
        Args:
            model_type: 新的模型类型
            device: 设备类型
        """
        model_key = model_type.value
        
        # 检查是否已经是当前模型
        if (self._current_model and 
            self._current_model.get_model_info().get('model_type') == model_key):
            self.logger.info(f"已经是当前模型: {model_key}")
            return
        
        # 检查模型是否已加载
        if model_key in self._models:
            self._current_model = self._models[model_key]
            self.logger.info(f"切换到已加载的模型: {model_key}")
            return
        
        # 加载新模型
        self.logger.info(f"加载新模型: {model_key}")
        
        model_class = self._model_registry.get_model_class(model_key)
        if model_class is None:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        # 创建新配置
        new_config = OCRConfig(
            model_type=model_type,
            device=self.config.device,
            **self.config.extra_config
        )
        
        model = model_class(new_config)
        await model.load_model(device)
        
        self._models[model_key] = model
        self._current_model = model
        
        self.logger.info(f"成功切换到模型: {model_key}")
    
    async def unload_all_models(self) -> None:
        """卸载所有模型"""
        self.logger.info("卸载所有模型")
        
        for model_key, model in self._models.items():
            try:
                await model.unload_model()
                self.logger.info(f"已卸载模型: {model_key}")
            except Exception as e:
                self.logger.error(f"卸载模型 {model_key} 失败: {e}")
        
        self._models.clear()
        self._current_model = None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self._stats.copy()
    
    def reset_stats(self) -> None:
        """重置性能统计"""
        self._stats = {
            'total_requests': 0,
            'total_processing_time': 0.0,
            'average_processing_time': 0.0,
            'error_count': 0
        }
    
    def list_available_models(self) -> List[str]:
        """列出可用的模型"""
        return self._model_registry.list_models()
    
    def _get_model(self, model_type: Optional[OCRModelType]) -> OCRInterface:
        """获取指定的模型"""
        if model_type is None:
            if self._current_model is None:
                raise RuntimeError("没有可用的模型")
            return self._current_model
        
        model_key = model_type.value
        if model_key not in self._models:
            raise ValueError(f"模型 {model_key} 未加载")
        
        return self._models[model_key]
    
    def _update_stats(self, processing_time: float) -> None:
        """更新性能统计"""
        self._stats['total_processing_time'] += processing_time
        self._stats['average_processing_time'] = (
            self._stats['total_processing_time'] / self._stats['total_requests']
        )
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.unload_all_models()
