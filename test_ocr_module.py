"""
OCR模块测试脚本

测试重构后的OCR模块是否能正常工作。
"""

import asyncio
import numpy as np
import sys
import traceback
from pathlib import Path

# 添加模块路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试配置模块
        from ocr_module.config import OCRConfig, ModelConfig, OCRConfigPresets, OCRModelType, DeviceType
        print("✅ 配置模块导入成功")
        
        # 测试核心模块
        from ocr_module.core import OCRInterface, BaseOCR, OCREngine
        from ocr_module.core.interfaces import TextRegion, OCRResult
        print("✅ 核心模块导入成功")
        
        # 测试模型模块
        from ocr_module.models import ModelRegistry, BaseOCRModel
        print("✅ 模型模块导入成功")
        
        # 测试主模块
        from ocr_module import OCREngine as MainOCREngine
        print("✅ 主模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False


def test_config_creation():
    """测试配置创建"""
    print("\n=== 测试配置创建 ===")
    
    try:
        from ocr_module.config import OCRConfig, OCRConfigPresets, OCRModelType, DeviceType
        
        # 测试基本配置
        config = OCRConfig(
            model_type=OCRModelType.OCR_48PX,
            device=DeviceType.CPU,
            prob_threshold=0.2
        )
        print(f"✅ 基本配置创建成功: {config.model_type.value}")
        
        # 测试预设配置
        high_acc_config = OCRConfigPresets.high_accuracy()
        fast_config = OCRConfigPresets.fast_processing()
        manga_config = OCRConfigPresets.manga_optimized()
        debug_config = OCRConfigPresets.debug_mode()
        
        print("✅ 预设配置创建成功")
        print(f"  - 高精度配置: {high_acc_config.model_type.value}")
        print(f"  - 快速配置: {fast_config.model_type.value}")
        print(f"  - 漫画配置: {manga_config.model_type.value}")
        print(f"  - 调试配置: {debug_config.model_type.value}")
        
        # 测试配置转换
        config_dict = config.to_dict()
        restored_config = OCRConfig.from_dict(config_dict)
        print("✅ 配置序列化/反序列化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置创建失败: {e}")
        traceback.print_exc()
        return False


def test_model_registry():
    """测试模型注册表"""
    print("\n=== 测试模型注册表 ===")
    
    try:
        from ocr_module.models.model_registry import ModelRegistry
        from ocr_module.models.base_model import BaseOCRModel
        from ocr_module.config import OCRConfig
        
        # 创建注册表
        registry = ModelRegistry()
        
        # 创建一个测试模型类
        class TestOCRModel(BaseOCRModel):
            async def _load_model_impl(self, device: str):
                pass
            
            async def _unload_model_impl(self):
                pass
            
            async def _infer_impl(self, image, regions, **kwargs):
                return []
        
        # 注册模型
        registry.register_model('test_model', TestOCRModel)
        print("✅ 模型注册成功")
        
        # 测试模型获取
        model_class = registry.get_model_class('test_model')
        assert model_class is not None
        print("✅ 模型获取成功")
        
        # 测试模型列表
        models = registry.list_models()
        assert 'test_model' in models
        print(f"✅ 模型列表: {models}")
        
        # 测试模型创建
        config = OCRConfig()
        model = registry.create_model('test_model', config)
        assert isinstance(model, TestOCRModel)
        print("✅ 模型创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型注册表测试失败: {e}")
        traceback.print_exc()
        return False


def test_text_region():
    """测试文本区域数据结构"""
    print("\n=== 测试文本区域数据结构 ===")
    
    try:
        from ocr_module.core.interfaces import TextRegion
        
        # 创建文本区域
        points = np.array([[10, 10], [100, 10], [100, 50], [10, 50]])
        region = TextRegion(
            points=points,
            text="测试文本",
            confidence=0.95,
            fg_color=(0, 0, 0),
            bg_color=(255, 255, 255)
        )
        
        print(f"✅ 文本区域创建成功: '{region.text}' (置信度: {region.confidence})")
        print(f"  - 坐标点数量: {len(region.points)}")
        print(f"  - 前景色: {region.fg_color}")
        print(f"  - 背景色: {region.bg_color}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文本区域测试失败: {e}")
        traceback.print_exc()
        return False


async def test_ocr_engine_basic():
    """测试OCR引擎基本功能"""
    print("\n=== 测试OCR引擎基本功能 ===")
    
    try:
        from ocr_module import OCREngine, OCRConfig, OCRModelType, DeviceType
        from ocr_module.core.interfaces import TextRegion
        
        # 创建配置
        config = OCRConfig(
            model_type=OCRModelType.OCR_48PX,
            device=DeviceType.CPU,
            verbose=True
        )
        
        # 创建引擎
        engine = OCREngine(config)
        print("✅ OCR引擎创建成功")
        
        # 测试可用模型列表
        available_models = engine.list_available_models()
        print(f"✅ 可用模型: {available_models}")
        
        # 测试性能统计
        stats = engine.get_stats()
        print(f"✅ 性能统计: {stats}")
        
        # 注意：这里不测试实际的模型加载，因为可能没有模型文件
        print("✅ OCR引擎基本功能测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR引擎测试失败: {e}")
        traceback.print_exc()
        return False


def test_network_creation():
    """测试网络创建"""
    print("\n=== 测试网络创建 ===")
    
    try:
        from ocr_module.models.model_48px.network import OCRNetwork, ConvNextFeatureExtractor
        
        # 创建特征提取器
        feature_extractor = ConvNextFeatureExtractor(
            img_height=48,
            in_dim=3,
            dim=320
        )
        print("✅ 特征提取器创建成功")
        
        # 创建OCR网络
        dictionary = ['<S>', '</S>', '<SP>'] + [chr(i) for i in range(ord('a'), ord('z')+1)]
        network = OCRNetwork(
            dictionary=dictionary,
            max_len=255,
            embedding_dim=320
        )
        print(f"✅ OCR网络创建成功，词汇量: {len(dictionary)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络创建测试失败: {e}")
        traceback.print_exc()
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始OCR模块测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置创建", test_config_creation),
        ("模型注册表", test_model_registry),
        ("文本区域", test_text_region),
        ("OCR引擎基本功能", test_ocr_engine_basic),
        ("网络创建", test_network_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OCR模块重构成功！")
    else:
        print("⚠️  部分测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(run_all_tests())
    
    if success:
        print("\n✨ OCR模块重构验证完成，可以开始使用新的模块化架构！")
    else:
        print("\n🔧 需要修复一些问题后再继续")
    
    sys.exit(0 if success else 1)
