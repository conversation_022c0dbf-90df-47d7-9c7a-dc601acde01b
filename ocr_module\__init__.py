"""
OCR模块 - 图片翻译项目的光学字符识别模块

这是一个模块化的OCR系统，支持多种OCR模型和配置。
主要功能包括：
- 文本检测和识别
- 多种模型支持（48px, 32px, MangaOCR等）
- 灵活的配置管理
- 可扩展的架构设计

使用示例：
    from ocr_module import OCREngine, OCRConfig
    
    config = OCRConfig(model_type='48px', prob_threshold=0.2)
    engine = OCREngine(config)
    
    # 加载模型
    await engine.load_model(device='cuda')
    
    # 执行OCR
    results = await engine.recognize(image, text_regions)
"""

from .config import OCRConfig, ModelConfig, OCRModelType, DeviceType, OCRConfigPresets
from .core import OCREngine, OCRInterface
from .models import get_available_models, create_model

__version__ = "1.0.0"
__author__ = "Image Translator Team"

__all__ = [
    'OCREngine',
    'OCRInterface',
    'OCRConfig',
    'OCRModelType',
    'DeviceType',
    'OCRConfigPresets',
    'ModelConfig',
    'get_available_models',
    'create_model'
]
