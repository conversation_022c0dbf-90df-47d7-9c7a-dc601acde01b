"""
简单的导入测试
"""

print("开始测试...")

try:
    print("1. 测试配置模块...")
    from ocr_module.config.ocr_config import OCRConfig, OCRModelType, DeviceType
    print("✅ 配置模块导入成功")
    
    print("2. 测试接口模块...")
    from ocr_module.core.interfaces import TextRegion, OCRInterface
    print("✅ 接口模块导入成功")
    
    print("3. 测试基础OCR...")
    from ocr_module.core.base_ocr import BaseOCR
    print("✅ 基础OCR导入成功")
    
    print("4. 测试模型注册表...")
    from ocr_module.models.model_registry import ModelRegistry
    print("✅ 模型注册表导入成功")
    
    print("5. 测试配置创建...")
    config = OCRConfig(
        model_type=OCRModelType.OCR_48PX,
        device=DeviceType.CPU
    )
    print(f"✅ 配置创建成功: {config.model_type.value}")
    
    print("6. 测试文本区域...")
    import numpy as np
    region = TextRegion(
        points=np.array([[0, 0], [100, 0], [100, 50], [0, 50]]),
        text="测试",
        confidence=0.9
    )
    print(f"✅ 文本区域创建成功: {region.text}")
    
    print("🎉 所有基础测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
