"""
模型模块 - OCR模型的实现

包含：
- 各种OCR模型的实现
- 模型注册和管理
- 模型工厂方法
"""

from .model_registry import ModelRegistry
from .base_model import BaseOCRModel

# 导入具体的模型实现
try:
    from .model_48px import Model48pxOCR
    _MODEL_48PX_AVAILABLE = True
except ImportError as e:
    print(f"Warning: 48px model not available: {e}")
    _MODEL_48PX_AVAILABLE = False

try:
    from .model_32px import Model32pxOCR
    _MODEL_32PX_AVAILABLE = True
except ImportError as e:
    print(f"Warning: 32px model not available: {e}")
    _MODEL_32PX_AVAILABLE = False


def get_available_models():
    """获取可用的模型列表"""
    registry = ModelRegistry()
    return registry.list_models()


def create_model(model_type: str, config):
    """创建模型实例"""
    registry = ModelRegistry()
    model_class = registry.get_model_class(model_type)
    if model_class is None:
        raise ValueError(f"不支持的模型类型: {model_type}")
    return model_class(config)


# 注册可用的模型
_registry = ModelRegistry()

if _MODEL_48PX_AVAILABLE:
    _registry.register_model('ocr48px', Model48pxOCR)
    _registry.register_model('ocr_48px', Model48pxOCR)

if _MODEL_32PX_AVAILABLE:
    _registry.register_model('ocr32px', Model32pxOCR)
    _registry.register_model('ocr_32px', Model32pxOCR)


__all__ = [
    'ModelRegistry',
    'BaseOCRModel',
    'get_available_models',
    'create_model'
]

# 如果模型可用，也导出它们
if _MODEL_48PX_AVAILABLE:
    __all__.append('Model48pxOCR')

if _MODEL_32PX_AVAILABLE:
    __all__.append('Model32pxOCR')
