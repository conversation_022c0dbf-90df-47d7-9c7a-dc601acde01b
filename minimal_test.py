"""
最小化测试
"""

print("开始最小化测试...")

try:
    print("1. 测试基本导入...")
    import sys
    print(f"Python版本: {sys.version}")
    
    print("2. 测试numpy...")
    import numpy as np
    print("✅ numpy导入成功")
    
    print("3. 测试枚举...")
    from enum import Enum
    
    class TestEnum(Enum):
        TEST = "test"
    
    print("✅ 枚举测试成功")
    
    print("4. 测试dataclass...")
    from dataclasses import dataclass
    
    @dataclass
    class TestClass:
        value: str = "test"
    
    test_obj = TestClass()
    print(f"✅ dataclass测试成功: {test_obj.value}")
    
    print("5. 测试OCR配置枚举...")
    from ocr_module.config.ocr_config import OCRModelType
    print(f"✅ OCR枚举导入成功: {OCRModelType.OCR_48PX.value}")
    
    print("🎉 最小化测试全部通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
