# DO NOT EDIT! This file was generated by jschema_to_python version 0.0.1.dev29,
# with extension for dataclasses and type annotation.

from __future__ import annotations

import dataclasses
from typing import Any, List, Literal, Optional

from torch.onnx._internal.diagnostics.infra.sarif import (
    _location,
    _property_bag,
    _reporting_descriptor_reference,
    _stack,
    _web_request,
    _web_response,
)


@dataclasses.dataclass
class ThreadFlowLocation(object):
    """A location visited by an analysis tool while simulating or monitoring the execution of a program."""

    execution_order: int = dataclasses.field(
        default=-1, metadata={"schema_property_name": "executionOrder"}
    )
    execution_time_utc: Optional[str] = dataclasses.field(
        default=None, metadata={"schema_property_name": "executionTimeUtc"}
    )
    importance: Literal["important", "essential", "unimportant"] = dataclasses.field(
        default="important", metadata={"schema_property_name": "importance"}
    )
    index: int = dataclasses.field(
        default=-1, metadata={"schema_property_name": "index"}
    )
    kinds: Optional[List[str]] = dataclasses.field(
        default=None, metadata={"schema_property_name": "kinds"}
    )
    location: Optional[_location.Location] = dataclasses.field(
        default=None, metadata={"schema_property_name": "location"}
    )
    module: Optional[str] = dataclasses.field(
        default=None, metadata={"schema_property_name": "module"}
    )
    nesting_level: Optional[int] = dataclasses.field(
        default=None, metadata={"schema_property_name": "nestingLevel"}
    )
    properties: Optional[_property_bag.PropertyBag] = dataclasses.field(
        default=None, metadata={"schema_property_name": "properties"}
    )
    stack: Optional[_stack.Stack] = dataclasses.field(
        default=None, metadata={"schema_property_name": "stack"}
    )
    state: Any = dataclasses.field(
        default=None, metadata={"schema_property_name": "state"}
    )
    taxa: Optional[
        List[_reporting_descriptor_reference.ReportingDescriptorReference]
    ] = dataclasses.field(default=None, metadata={"schema_property_name": "taxa"})
    web_request: Optional[_web_request.WebRequest] = dataclasses.field(
        default=None, metadata={"schema_property_name": "webRequest"}
    )
    web_response: Optional[_web_response.WebResponse] = dataclasses.field(
        default=None, metadata={"schema_property_name": "webResponse"}
    )


# flake8: noqa
